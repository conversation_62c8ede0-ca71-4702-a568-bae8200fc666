using System;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using Avalonia.Controls.Embedding;
using Avalonia.Input;
using Avalonia.Platform;
using Control = Avalonia.Controls.Control;

namespace AvaloniaHwndHost;

public class AvaloniaHwndHost : HwndHost
{
    private EmbeddableControlRoot _root;

    public AvaloniaHwndHost()
    {
        DataContextChanged += AvaloniaHwndHost_DataContextChanged;
    }

    //Switching documents in Revit causes the Panel content to "reset",
    //as a consequence BuildWindowCore gets called again the _root needs to be initialized again
    private EmbeddableControlRoot root
    {
        get
        {
            if (_root == null)
            {
                _root = new EmbeddableControlRoot();
            }

            return _root;
        }
    }

    public Control Content
    {
        get => (Control)root.Content;
        set
        {
            root.Content = value;
            if (value != null)
            {
                value.DataContext = DataContext;
            }
        }
    }

    private void AvaloniaHwndHost_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
    {
        if (Content != null)
        {
            Content.DataContext = e.NewValue;
        }
    }

    protected override HandleRef BuildWindowCore(HandleRef hwndParent)
    {
        // In Avalonia 11.3.2, Prepare() and Renderer.Start() are no longer needed
        // The EmbeddableControlRoot handles initialization automatically

        // Use TryGetPlatformHandle() to get the platform handle in Avalonia 11.3.2
        var platformHandle = root.TryGetPlatformHandle();
        var handle = platformHandle?.Handle ?? IntPtr.Zero;

        //var wpfWindow = Window.GetWindow(this);
        //var parentHandle = new WindowInteropHelper(wpfWindow).Handle;
        _ = UnmanagedMethods.SetParent(handle, hwndParent.Handle);

        if (IsFocused)
        {
            // Focus Avalonia, if host was focused before handle was created.
            // In Avalonia 11.3.2, access FocusManager through the TopLevel
            root.FocusManager?.GetFocusedElement()?.Focus();
        }

        return new HandleRef(root, handle);
    }

    protected override void DestroyWindowCore(HandleRef hwnd)
    {
        root.Dispose();
    }
}