using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;

namespace RevitAvalonia;

public partial class App : Avalonia.Application
{
    public override void Initialize()
    {
        AvaloniaXamlLoader.Load(this);
    }

    public override void OnFrameworkInitializationCompleted()
    {
        // For Revit integration, we don't set up a traditional desktop lifetime
        // The application lifecycle is managed by Revit
        base.OnFrameworkInitializationCompleted();
    }
}
