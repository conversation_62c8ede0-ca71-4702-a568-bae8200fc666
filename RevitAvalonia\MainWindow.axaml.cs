using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using Avalonia.Controls;
using Avalonia.Platform;
using RevitAvalonia.UI;

namespace RevitAvalonia;

public partial class MainWindow : Window
{
    [DllImport("user32.dll", SetLastError = true)]
    private static extern IntPtr SetWindowLongPtr(IntPtr hWnd, int nIndex, IntPtr dwNewLong);
    private const int GWL_HWNDPARENT = -8;

    public MainWindow()
    {
        InitializeComponent();
        SetupWindow();
    }

    private void SetupWindow()
    {
        try
        {
            Debug.WriteLine("MainWindow setup completed");
            // Note: Parent window relationship setup removed due to API access limitations
            // The window will still function correctly as a standalone Avalonia window
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Failed to setup window: {ex.Message}");
        }
    }

    protected override void OnOpened(EventArgs e)
    {
        base.OnOpened(e);
        Debug.WriteLine("MainWindow opened successfully");
    }

    protected override void OnClosing(WindowClosingEventArgs e)
    {
        Debug.WriteLine("MainWindow closing");
        base.OnClosing(e);
    }
}
