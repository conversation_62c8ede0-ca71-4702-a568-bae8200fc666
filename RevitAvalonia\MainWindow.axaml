<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:views="clr-namespace:RevitAvalonia.UI.Views"
        mc:Ignorable="d" d:DesignWidth="450" d:DesignHeight="700"
        x:Class="RevitAvalonia.MainWindow"
        Title="Revit Avalonia Add-in"
        Width="450"
        Height="700"
        MinWidth="400"
        MinHeight="600"
        WindowStartupLocation="CenterScreen"
        CanResize="True"
        Icon="/Resources/Icons/RibbonIcon32.png">
    
    <Grid>
        <views:RevitMainView />
    </Grid>
    
</Window>
