﻿using Autodesk.Revit.Attributes;
using Nice3point.Revit.Toolkit.External;
using RevitAvalonia.UI;
using Avalonia.Threading;

namespace RevitAvalonia.Commands;

/// <summary>
///     External command entry point
/// </summary>
[UsedImplicitly]
[Transaction(TransactionMode.Manual)]
public class StartupCommand : ExternalCommand
{
    public override void Execute()
    {
        try
        {
            // Set the Revit context for the UI
            RevitContext.UiApplication = UiApplication;

            // Try to initialize and show the Avalonia window
            if (TryShowAvaloniaWindow())
            {
                return; // Success - Avalonia window is shown
            }

            // Fallback to simple message if Avalonia fails
            ShowFallbackMessage();
        }
        catch (System.Exception ex)
        {
            Autodesk.Revit.UI.TaskDialog.Show("Error",
                $"Failed to launch add-in window: {ex.Message}\n\nStack trace:\n{ex.StackTrace}");
        }
    }

    private bool TryShowAvaloniaWindow()
    {
        try
        {
            // Ensure we're on the UI thread for Avalonia operations
            if (Dispatcher.UIThread.CheckAccess())
            {
                var window = new MainWindow();
                window.Show();
            }
            else
            {
                Dispatcher.UIThread.InvokeAsync(() =>
                {
                    var window = new MainWindow();
                    window.Show();
                });
            }
            return true;
        }
        catch (System.Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Avalonia window failed: {ex}");
            return false;
        }
    }

    private void ShowFallbackMessage()
    {
        try
        {
            // Show a simple message dialog as fallback
            Autodesk.Revit.UI.TaskDialog.Show("Add-in Launch Failed",
                "Failed to initialize the Avalonia UI.\n\n" +
                "This could be due to:\n" +
                "• Graphics context conflicts with Revit\n" +
                "• Missing Avalonia dependencies\n" +
                "• Incompatible rendering backend\n\n" +
                "Please check the Revit console for detailed error information.");
        }
        catch (System.Exception ex)
        {
            throw new System.InvalidOperationException("Failed to show fallback message", ex);
        }
    }
}