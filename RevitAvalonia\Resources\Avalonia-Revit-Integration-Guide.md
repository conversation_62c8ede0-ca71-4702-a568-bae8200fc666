﻿# Integrating Avalonia UI Framework with Revit API: A Comprehensive Technical Guide

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [Project Setup and Dependencies](#project-setup-and-dependencies)
3. [Step-by-Step Implementation Guide](#step-by-step-implementation-guide)
4. [Code Examples](#code-examples)
5. [Best Practices](#best-practices)
6. [Common Pitfalls and Solutions](#common-pitfalls-and-solutions)
7. [Advanced Topics](#advanced-topics)

## Architecture Overview

The Speckle Revit connector demonstrates a sophisticated hybrid architecture that embeds Avalonia UI within Revit's WPF-based environment. This integration is achieved through several key components:

### Core Components

1. **AvaloniaHwndHost**: A custom WPF `HwndHost` control that bridges WPF and Avalonia
2. **EmbeddableControlRoot**: Avalonia's mechanism for hosting UI in external applications
3. **APIContext**: Threading abstraction for safe Revit API access
4. **ConnectorBindings**: MVVM bridge between UI and host application
5. **ExternalEvents**: Revit's mechanism for thread-safe API operations

### Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    Revit Application                        │
│  ┌─────────────────────────────────────────────────────┐    │
│  │                WPF Layer                            │    │
│  │  ┌─────────────────────────────────────────────┐    │    │
│  │  │           AvaloniaHwndHost                  │    │    │
│  │  │  ┌─────────────────────────────────────┐    │    │    │
│  │  │  │        Avalonia UI Layer            │    │    │    │
│  │  │  │  ┌─────────────────────────────┐    │    │    │    │
│  │  │  │  │     ViewModels (MVVM)       │    │    │    │    │
│  │  │  │  └─────────────────────────────┘    │    │    │    │
│  │  │  └─────────────────────────────────────┘    │    │    │
│  │  └─────────────────────────────────────────────┘    │    │
│  └─────────────────────────────────────────────────────┘    │
│                           │                                 │
│                           ▼                                 │
│  ┌─────────────────────────────────────────────────────┐    │
│  │              APIContext + ExternalEvents            │    │
│  └─────────────────────────────────────────────────────┘    │
│                           │                                 │
│                           ▼                                 │
│  ┌─────────────────────────────────────────────────────┐    │
│  │                 Revit API                           │    │
│  └─────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

## Project Setup and Dependencies

### Required NuGet Packages

```xml
<!-- Core Avalonia packages -->
<PackageReference Include="Avalonia" Version="0.10.21" />
<PackageReference Include="Avalonia.Desktop" Version="0.10.21" />
<PackageReference Include="Avalonia.ReactiveUI" Version="0.10.21" />

<!-- Material Design (optional but recommended) -->
<PackageReference Include="Speckle.Material.Avalonia" Version="3.0.4" />
<PackageReference Include="Speckle.Material.Icons.Avalonia" Version="1.2.0" />

<!-- Revit API -->
<PackageReference Include="Speckle.Revit.API" Version="2023.0.0" />
```

### Project Structure

```
YourRevitConnector/
├── Entry/
│   ├── App.cs                    # Main Revit application entry point
│   └── RevitCommand.cs           # Command implementation
├── UI/
│   ├── AvaloniaHwndHost.cs       # WPF-Avalonia bridge
│   ├── Panel.xaml                # WPF dockable panel
│   ├── Panel.xaml.cs             # Panel code-behind
│   └── ConnectorBindings.cs      # MVVM bridge
├── Threading/
│   └── APIContext.cs             # Threading abstraction
├── AvaloniaApp/
│   ├── App.xaml                  # Avalonia application
│   ├── App.xaml.cs               # Avalonia app code-behind
│   ├── ViewModels/               # MVVM ViewModels
│   └── Views/                    # Avalonia Views
└── YourConnector.addin           # Revit add-in manifest
```

## Step-by-Step Implementation Guide

### Step 1: Create the Revit Add-in Entry Point

The main entry point follows the standard Revit add-in pattern but includes Avalonia initialization:

```csharp
// Entry/App.cs
using System;
using System.Reflection;
using Autodesk.Revit.ApplicationServices;
using Autodesk.Revit.UI;
using YourConnector.Threading;
using YourConnector.UI;

namespace YourConnector.Entry
{
    public class App : IExternalApplication
    {
        public static UIControlledApplication UICtrlApp { get; private set; }
        public static UIApplication AppInstance { get; private set; }
        private static bool _initialized = false;

        public Result OnStartup(UIControlledApplication application)
        {
            try
            {
                // Essential: Set up assembly resolution before anything else
                AppDomain.CurrentDomain.AssemblyResolve += OnAssemblyResolve;
                
                UICtrlApp = application;
                
                // Initialize threading infrastructure
                APIContext.Initialize(application);
                
                // Set up Revit events
                application.ControlledApplication.ApplicationInitialized += 
                    ControlledApplication_ApplicationInitialized;
                application.ControlledApplication.DocumentOpening += 
                    ControlledApplication_DocumentOpening;
                
                // Create Revit UI elements
                CreateRibbonPanel(application);
                
                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                // Log error appropriately
                return Result.Failed;
            }
        }

        private void ControlledApplication_ApplicationInitialized(object sender, 
            Autodesk.Revit.DB.Events.ApplicationInitializedEventArgs e)
        {
            try
            {
                InitializeConnector();
                AppInstance ??= new UIApplication(sender as Application);
                RevitCommand.RegisterPane();
            }
            catch (Exception ex)
            {
                // Handle initialization errors
            }
        }

        private void ControlledApplication_DocumentOpening(object sender, 
            Autodesk.Revit.DB.Events.DocumentOpeningEventArgs e)
        {
            // Handle case where document opens before ApplicationInitialized
            try
            {
                InitializeConnector();
                AppInstance ??= new UIApplication(sender as Application);
                RevitCommand.RegisterPane();
            }
            catch (Exception ex)
            {
                // Handle errors
            }
        }

        private void InitializeConnector()
        {
            if (_initialized) return;
            
            // Pre-build Avalonia app for faster startup
            RevitCommand.InitAvalonia();
            _initialized = true;
        }

        private void CreateRibbonPanel(UIControlledApplication application)
        {
            string tabName = "Your Connector";
            
            try
            {
                application.CreateRibbonTab(tabName);
            }
            catch (Autodesk.Revit.Exceptions.ArgumentException)
            {
                // Tab already exists
            }

            var panel = application.CreateRibbonPanel(tabName, "Main Panel");
            
            var button = panel.AddItem(new PushButtonData(
                "YourConnector",
                "Your Connector",
                Assembly.GetExecutingAssembly().Location,
                typeof(RevitCommand).FullName
            )) as PushButton;
            
            button.ToolTip = "Open Your Connector";
            // Set button images here
        }

        private Assembly OnAssemblyResolve(object sender, ResolveEventArgs args)
        {
            // Implement custom assembly resolution logic
            // This is crucial for loading Avalonia dependencies
            return null;
        }

        public Result OnShutdown(UIControlledApplication application)
        {
            return Result.Succeeded;
        }
    }
}
```

### Step 2: Implement the Revit Command

```csharp
// Entry/RevitCommand.cs
using System;
using System.Runtime.InteropServices;
using Avalonia;
using Avalonia.Controls;
using Avalonia.ReactiveUI;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using YourConnector.AvaloniaApp;
using YourConnector.UI;

namespace YourConnector.Entry
{
    [Transaction(TransactionMode.Manual)]
    public class RevitCommand : IExternalCommand
    {
        public static DockablePaneId PanelId = new DockablePaneId(new Guid("12345678-1234-1234-1234-123456789012"));
        public static bool UseDockablePanel = true;
        private static Panel _panel;
        private static Window MainWindow;
        private static Avalonia.Application AvaloniaApp;

        [DllImport("user32.dll", SetLastError = true)]
        private static extern IntPtr SetWindowLongPtr(IntPtr hWnd, int nIndex, IntPtr dwNewLong);
        private const int GWL_HWNDPARENT = -8;

        public static void InitAvalonia()
        {
            BuildAvaloniaApp().SetupWithoutStarting();
        }

        public static AppBuilder BuildAvaloniaApp() =>
            AppBuilder
                .Configure<App>()
                .UsePlatformDetect()
                .With(new SkiaOptions { MaxGpuResourceSizeBytes = 8096000 })
                .With(new Win32PlatformOptions { 
                    AllowEglInitialization = true, 
                    EnableMultitouch = false 
                })
                .LogToTrace()
                .UseReactiveUI();

        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            if (UseDockablePanel)
            {
                RegisterPane();
                var panel = App.AppInstance.GetDockablePane(PanelId);
                panel.Show();
            }
            else
            {
                CreateOrFocusWindow();
            }

            return Result.Succeeded;
        }

        internal static void RegisterPane()
        {
            if (!UseDockablePanel) return;

            var registered = DockablePane.PaneIsRegistered(PanelId);
            var created = DockablePane.PaneExists(PanelId);

            if (registered && created)
            {
                _panel.Init();
                return;
            }

            if (!registered)
            {
                _panel = new Panel();
                App.AppInstance.RegisterDockablePane(PanelId, "Your Connector", _panel);
                _panel.Init();
            }
        }

        private static void CreateOrFocusWindow()
        {
            bool showWindow = false;

            if (MainWindow == null)
            {
                if (AvaloniaApp == null)
                {
                    var app = BuildAvaloniaApp();
                    app.Start(AppMain, Array.Empty<string>());
                }

                MainWindow = new MainWindow();
                showWindow = true;
            }

            if (showWindow)
            {
                MainWindow.Show();
                MainWindow.Activate();

                // Set Revit as parent window
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    var parentHwnd = App.AppInstance.MainWindowHandle;
                    var hwnd = MainWindow.PlatformImpl.Handle.Handle;
                    SetWindowLongPtr(hwnd, GWL_HWNDPARENT, parentHwnd);
                }
            }
        }

        private static void AppMain(Avalonia.Application app, string[] args)
        {
            AvaloniaApp = app;
        }
    }
}
```

### Step 3: Create the AvaloniaHwndHost Bridge

```csharp
// UI/AvaloniaHwndHost.cs
using System;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using Avalonia.Controls;
using Avalonia.Controls.Embedding;
using Avalonia.Input;
using Avalonia.Platform;

namespace YourConnector.UI
{
    public class AvaloniaHwndHost : HwndHost
    {
        private EmbeddableControlRoot _root;

        public AvaloniaHwndHost()
        {
            DataContextChanged += AvaloniaHwndHost_DataContextChanged;
        }

        // Lazy initialization to handle document switching in Revit
        private EmbeddableControlRoot Root
        {
            get
            {
                if (_root == null || _root.Renderer == null)
                {
                    _root = new EmbeddableControlRoot();
                }
                return _root;
            }
        }

        public Control Content
        {
            get => (Control)Root.Content;
            set
            {
                Root.Content = value;
                if (value != null)
                {
                    value.DataContext = DataContext;
                }
            }
        }

        private void AvaloniaHwndHost_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (Content != null)
            {
                Content.DataContext = e.NewValue;
            }
        }

        protected override HandleRef BuildWindowCore(HandleRef hwndParent)
        {
            Root.Prepare();
            Root.Renderer.Start();

            var handle = ((IWindowImpl)Root.PlatformImpl)?.Handle?.Handle ?? IntPtr.Zero;
            UnmanagedMethods.SetParent(handle, hwndParent.Handle);

            if (IsFocused)
            {
                FocusManager.Instance.Focus(null);
            }

            return new HandleRef(Root, handle);
        }

        protected override void DestroyWindowCore(HandleRef hwnd)
        {
            Root?.Dispose();
        }
    }

    internal static class UnmanagedMethods
    {
        [DllImport("user32.dll")]
        public static extern bool SetParent(IntPtr hWnd, IntPtr hWndNewParent);
    }
}
```

### Step 4: Create the WPF Dockable Panel

```xml
<!-- UI/Panel.xaml -->
<Page x:Class="YourConnector.UI.Panel"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:local="clr-namespace:YourConnector.UI"
      Background="White"
      Width="400"
      Height="750">
    <Grid>
        <local:AvaloniaHwndHost x:Name="AvaloniaHost" />
    </Grid>
</Page>
```

```csharp
// UI/Panel.xaml.cs
using System;
using System.Windows;
using System.Windows.Controls;
using Autodesk.Revit.UI;
using YourConnector.AvaloniaApp.Views;

namespace YourConnector.UI
{
    public partial class Panel : Page, IDockablePaneProvider
    {
        public Panel()
        {
            InitializeComponent();
            AvaloniaHost.MessageHook += AvaloniaHost_MessageHook;
        }

        private const uint DLGC_WANTARROWS = 0x0001;
        private const uint DLGC_HASSETSEL = 0x0008;
        private const uint DLGC_WANTCHARS = 0x0080;
        private const uint WM_GETDLGCODE = 0x0087;

        /// <summary>
        /// Ensures text input events are passed to Avalonia control
        /// </summary>
        private IntPtr AvaloniaHost_MessageHook(IntPtr hwnd, int msg, IntPtr wParam, IntPtr lParam, ref bool handled)
        {
            if (msg != WM_GETDLGCODE) return IntPtr.Zero;

            handled = true;
            return new IntPtr(DLGC_WANTCHARS | DLGC_WANTARROWS | DLGC_HASSETSEL);
        }

        /// <summary>
        /// Reinitialize when switching documents in Revit
        /// </summary>
        public void Init()
        {
            AvaloniaHost.Content = new MainUserControl();
        }

        public void SetupDockablePane(DockablePaneProviderData data)
        {
            data.FrameworkElement = this as FrameworkElement;
            data.InitialState = new DockablePaneState
            {
                DockPosition = DockPosition.Tabbed,
                TabBehind = DockablePanes.BuiltInDockablePanes.ProjectBrowser
            };
        }
    }
}
```

### Step 5: Implement Threading Abstraction (APIContext)

```csharp
// Threading/APIContext.cs
using System;
using System.Threading;
using System.Threading.Tasks;
using Autodesk.Revit.UI;

namespace YourConnector.Threading
{
    /// <summary>
    /// Provides thread-safe access to Revit API from any context
    /// </summary>
    public static class APIContext
    {
        private static SemaphoreSlim semaphore = new(1, 1);
        private static UIControlledApplication uiApplication;
        private static ExternalEventHandler<IExternalEventHandler, ExternalEvent> factoryExternalEventHandler;
        private static ExternalEvent factoryExternalEvent;

        public static void Initialize(UIControlledApplication application)
        {
            uiApplication = application;
            factoryExternalEventHandler = new(ExternalEvent.Create);
            factoryExternalEvent = ExternalEvent.Create(factoryExternalEventHandler);
        }

        public static async Task<TResult> Run<TResult>(Func<UIControlledApplication, TResult> func)
        {
            await semaphore.WaitAsync().ConfigureAwait(false);
            try
            {
                var handler = new ExternalEventHandler<UIControlledApplication, TResult>(func);
                using var externalEvent = await Run(factoryExternalEventHandler, handler, factoryExternalEvent)
                    .ConfigureAwait(false);

                return await Run(handler, uiApplication, externalEvent).ConfigureAwait(false);
            }
            finally
            {
                semaphore.Release();
            }
        }

        public static async Task Run(Action<UIControlledApplication> action)
        {
            await Run<object>(app =>
            {
                action(app);
                return null;
            }).ConfigureAwait(false);
        }

        public static async Task Run(Action action)
        {
            await Run<object>(_ =>
            {
                action();
                return null;
            }).ConfigureAwait(false);
        }

        private static async Task<TResult> Run<TParameter, TResult>(
            ExternalEventHandler<TParameter, TResult> handler,
            TParameter parameter,
            ExternalEvent externalEvent)
        {
            var task = handler.GetTask(parameter);
            externalEvent.Raise();
            return await task.ConfigureAwait(false);
        }
    }

    internal class ExternalEventHandler<TParameter, TResult> : IExternalEventHandler
    {
        public TaskCompletionSource<TResult> Result { get; private set; }
        private readonly Func<TParameter, TResult> func;

        public ExternalEventHandler(Func<TParameter, TResult> func)
        {
            this.func = func;
        }

        public Task<TResult> GetTask(TParameter parameter)
        {
            Parameter = parameter;
            Result = new TaskCompletionSource<TResult>();
            return Result.Task;
        }

        public TParameter Parameter { get; private set; }

        public void Execute(UIApplication app)
        {
            try
            {
                var result = func(Parameter);
                Result.SetResult(result);
            }
            catch (Exception ex)
            {
                Result.SetException(ex);
            }
        }

        public string GetName() => "YourConnectorEventHandler";
    }
}
```

### Step 6: Create Avalonia Application

```xml
<!-- AvaloniaApp/App.xaml -->
<Application x:Class="YourConnector.AvaloniaApp.App"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:themes="clr-namespace:Material.Styles.Themes;assembly=Material.Styles">
    <Application.DataTemplates>
        <local:ViewLocator />
    </Application.DataTemplates>
    <Application.Styles>
        <themes:MaterialThemeBase />
        <StyleInclude Source="avares://Material.Avalonia/Material.Avalonia.Templates.xaml" />
        <StyleInclude Source="avares://Material.Icons.Avalonia/App.xaml" />
    </Application.Styles>
</Application>
```

```csharp
// AvaloniaApp/App.xaml.cs
using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using YourConnector.AvaloniaApp.ViewModels;
using YourConnector.AvaloniaApp.Views;
using Material.Styles.Themes;

namespace YourConnector.AvaloniaApp
{
    public class App : Application
    {
        public static readonly Color Primary = Color.FromRgb(59, 130, 246);
        public static readonly Color Accent = Color.FromRgb(255, 191, 0);

        public override void Initialize()
        {
            AvaloniaXamlLoader.Load(this);
            Name = "Your Connector";
        }

        public override void OnFrameworkInitializationCompleted()
        {
            var theme = Theme.Create(Theme.Light, Primary, Accent);
            var themeBootstrap = this.LocateMaterialTheme<MaterialThemeBase>();
            themeBootstrap.CurrentTheme = theme;

            if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
            {
                desktop.MainWindow = new MainWindow
                {
                    DataContext = new MainViewModel()
                };
            }

            base.OnFrameworkInitializationCompleted();
        }
    }
}
```

## Code Examples

### Complete MainViewModel Implementation

```csharp
// AvaloniaApp/ViewModels/MainViewModel.cs
using System;
using System.Collections.Generic;
using System.Reactive;
using System.Threading.Tasks;
using ReactiveUI;
using YourConnector.UI;

namespace YourConnector.AvaloniaApp.ViewModels
{
    public class MainViewModel : ReactiveObject
    {
        private ConnectorBindings _bindings;
        private List<string> _elements;
        private bool _isLoading;

        public MainViewModel()
        {
            // In a real implementation, inject this through DI
            _bindings = GetConnectorBindings();

            LoadElementsCommand = ReactiveCommand.CreateFromTask(LoadElementsAsync);
            CreateElementCommand = ReactiveCommand.CreateFromTask<string>(CreateElementAsync);
        }

        public string Title => $"Your Connector for {_bindings.GetHostAppNameVersion()}";

        public List<string> Elements
        {
            get => _elements;
            set => this.RaiseAndSetIfChanged(ref _elements, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => this.RaiseAndSetIfChanged(ref _isLoading, value);
        }

        public ReactiveCommand<Unit, Unit> LoadElementsCommand { get; }
        public ReactiveCommand<string, Unit> CreateElementCommand { get; }

        private async Task LoadElementsAsync()
        {
            try
            {
                IsLoading = true;
                Elements = await _bindings.GetElementsAsync();
            }
            catch (Exception ex)
            {
                // Handle error
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task CreateElementAsync(string elementData)
        {
            try
            {
                await _bindings.CreateElementAsync(elementData);
                await LoadElementsAsync(); // Refresh list
            }
            catch (Exception ex)
            {
                // Handle error
            }
        }

        private ConnectorBindings GetConnectorBindings()
        {
            // In a real implementation, use dependency injection
            return new RevitConnectorBindings(App.AppInstance);
        }
    }
}
```

## Best Practices

### 1. Threading Safety

**Always use APIContext for Revit API calls:**
```csharp
// ✅ Correct - Thread-safe
await APIContext.Run(app =>
{
    using var transaction = new Transaction(document, "Operation");
    transaction.Start();
    // Revit API operations here
    transaction.Commit();
});

// ❌ Incorrect - Not thread-safe
using var transaction = new Transaction(document, "Operation");
transaction.Start();
// This will throw exceptions if called from Avalonia UI thread
```

**Use ConfigureAwait(false) for library code:**
```csharp
var result = await APIContext.Run(operation).ConfigureAwait(false);
```

### 2. Resource Management

**Proper disposal of Avalonia resources:**
```csharp
protected override void DestroyWindowCore(HandleRef hwnd)
{
    try
    {
        Root?.Dispose();
        _root = null;
    }
    catch (Exception ex)
    {
        // Log but don't throw in disposal
    }
}
```

**Handle document switching:**
```csharp
public void Init()
{
    // Always reinitialize when documents switch
    AvaloniaHost.Content?.Dispose();
    AvaloniaHost.Content = new MainUserControl();
}
```

### 3. Lifecycle Handling

**Initialize Avalonia early but lazily:**
```csharp
private void InitializeConnector()
{
    if (_initialized) return;

    // Pre-build but don't start Avalonia
    RevitCommand.InitAvalonia();
    _initialized = true;
}
```

**Handle both initialization paths:**
```csharp
// Handle normal startup
application.ControlledApplication.ApplicationInitialized += OnInitialized;

// Handle document-first startup (double-click .rvt file)
application.ControlledApplication.DocumentOpening += OnDocumentOpening;
```

### 4. Error Handling

**Graceful degradation:**
```csharp
try
{
    await APIContext.Run(operation);
}
catch (Autodesk.Revit.Exceptions.InvalidOperationException ex)
{
    // Handle Revit-specific errors
    ShowUserFriendlyError("Operation not available in current context");
}
catch (Exception ex)
{
    // Log and show generic error
    Logger.Error(ex, "Unexpected error in operation");
    ShowUserFriendlyError("An unexpected error occurred");
}
```

## Common Pitfalls and Solutions

### 1. Assembly Loading Issues

**Problem**: Avalonia assemblies not found at runtime.

**Solution**: Implement custom assembly resolver:
```csharp
private Assembly OnAssemblyResolve(object sender, ResolveEventArgs args)
{
    var assemblyName = new AssemblyName(args.Name);
    var assemblyPath = Path.Combine(
        Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location),
        assemblyName.Name + ".dll");

    if (File.Exists(assemblyPath))
    {
        return Assembly.LoadFrom(assemblyPath);
    }

    return null;
}
```

### 2. Threading Violations

**Problem**: "Cross-thread operation not valid" exceptions.

**Solution**: Always use APIContext for Revit API calls:
```csharp
// ❌ Wrong - Direct API call from UI thread
var elements = new FilteredElementCollector(document).ToElements();

// ✅ Correct - Use APIContext
var elements = await APIContext.Run(app =>
    new FilteredElementCollector(CurrentDoc.Document).ToElements().ToList());
```

### 3. Memory Leaks

**Problem**: Avalonia controls not properly disposed.

**Solution**: Implement proper disposal patterns:
```csharp
public void Dispose()
{
    try
    {
        AvaloniaHost?.Content?.Dispose();
        Root?.Dispose();
        _eventHandler?.UnregisterEvents();
    }
    catch (Exception ex)
    {
        // Log but don't throw in disposal
    }
}
```

### 4. Document Switching Issues

**Problem**: UI becomes unresponsive when switching documents.

**Solution**: Reinitialize on document events:
```csharp
private void Application_DocumentOpened(object sender, DocumentOpenedEventArgs e)
{
    // Close any open dialogs
    if (MainViewModel.Instance.DialogBody != null)
    {
        CurrentOperationCancellation?.Cancel();
        MainViewModel.CloseDialog();
    }

    // Reinitialize panel
    RegisterPane();

    // Refresh UI state
    MainViewModel.Instance.NavigateToDefaultScreen();
}
```

### 5. Input Event Issues

**Problem**: Keyboard input not working in Avalonia controls.

**Solution**: Implement message hook:
```csharp
private IntPtr AvaloniaHost_MessageHook(IntPtr hwnd, int msg, IntPtr wParam, IntPtr lParam, ref bool handled)
{
    if (msg != WM_GETDLGCODE) return IntPtr.Zero;

    handled = true;
    return new IntPtr(DLGC_WANTCHARS | DLGC_WANTARROWS | DLGC_HASSETSEL);
}
```

## Advanced Topics

### 1. Custom Dependency Injection

Implement a proper DI container for better testability and maintainability:

```csharp
// Services/ServiceContainer.cs
public static class ServiceContainer
{
    private static readonly Dictionary<Type, object> _services = new();

    public static void Register<TInterface, TImplementation>(TImplementation instance)
        where TImplementation : class, TInterface
    {
        _services[typeof(TInterface)] = instance;
    }

    public static T Resolve<T>()
    {
        if (_services.TryGetValue(typeof(T), out var service))
        {
            return (T)service;
        }
        throw new InvalidOperationException($"Service {typeof(T).Name} not registered");
    }
}

// Usage in App.cs
private void InitializeServices()
{
    ServiceContainer.Register<ConnectorBindings>(new RevitConnectorBindings(AppInstance));
    ServiceContainer.Register<ILogger>(new SerilogLogger());
}
```

### 2. Advanced Event Handling

Implement a robust event system for complex UI interactions:

```csharp
// Events/EventAggregator.cs
public interface IEventAggregator
{
    void Subscribe<T>(Action<T> handler);
    void Publish<T>(T eventData);
}

public class EventAggregator : IEventAggregator
{
    private readonly Dictionary<Type, List<object>> _handlers = new();

    public void Subscribe<T>(Action<T> handler)
    {
        if (!_handlers.ContainsKey(typeof(T)))
            _handlers[typeof(T)] = new List<object>();

        _handlers[typeof(T)].Add(handler);
    }

    public void Publish<T>(T eventData)
    {
        if (_handlers.TryGetValue(typeof(T), out var handlers))
        {
            foreach (var handler in handlers.Cast<Action<T>>())
            {
                try
                {
                    handler(eventData);
                }
                catch (Exception ex)
                {
                    // Log error but continue with other handlers
                }
            }
        }
    }
}

// Usage
public class DocumentChangedEvent
{
    public string DocumentName { get; set; }
    public string DocumentPath { get; set; }
}

// In ViewModel
_eventAggregator.Subscribe<DocumentChangedEvent>(OnDocumentChanged);

// In Revit event handler
_eventAggregator.Publish(new DocumentChangedEvent
{
    DocumentName = e.Document.Title,
    DocumentPath = e.Document.PathName
});
```

### 3. Performance Optimization

Implement UI virtualization for large datasets:

```csharp
// ViewModels/VirtualizedListViewModel.cs
public class VirtualizedListViewModel : ReactiveObject
{
    private readonly ObservableCollection<ElementViewModel> _items = new();
    private readonly int _pageSize = 100;
    private int _currentPage = 0;

    public ReadOnlyObservableCollection<ElementViewModel> Items { get; }

    public VirtualizedListViewModel()
    {
        Items = new ReadOnlyObservableCollection<ElementViewModel>(_items);
        LoadMoreCommand = ReactiveCommand.CreateFromTask(LoadMoreAsync);
    }

    public ReactiveCommand<Unit, Unit> LoadMoreCommand { get; }

    private async Task LoadMoreAsync()
    {
        var newItems = await LoadPageAsync(_currentPage, _pageSize);
        foreach (var item in newItems)
        {
            _items.Add(item);
        }
        _currentPage++;
    }

    private async Task<List<ElementViewModel>> LoadPageAsync(int page, int pageSize)
    {
        return await APIContext.Run(app =>
        {
            var collector = new FilteredElementCollector(CurrentDoc.Document)
                .Skip(page * pageSize)
                .Take(pageSize);

            return collector.Select(e => new ElementViewModel(e)).ToList();
        });
    }
}
```

### 4. Custom Avalonia Controls

Create reusable custom controls for Revit-specific functionality:

```csharp
// Controls/RevitElementPicker.cs
public class RevitElementPicker : UserControl
{
    public static readonly StyledProperty<string> SelectedElementIdProperty =
        AvaloniaProperty.Register<RevitElementPicker, string>(nameof(SelectedElementId));

    public string SelectedElementId
    {
        get => GetValue(SelectedElementIdProperty);
        set => SetValue(SelectedElementIdProperty, value);
    }

    public ReactiveCommand<Unit, Unit> PickElementCommand { get; }

    public RevitElementPicker()
    {
        PickElementCommand = ReactiveCommand.CreateFromTask(PickElementAsync);
        InitializeComponent();
    }

    private async Task PickElementAsync()
    {
        var elementId = await APIContext.Run(app =>
        {
            var selection = CurrentDoc.Selection;
            var reference = selection.PickObject(ObjectType.Element, "Select an element");
            return reference.ElementId.IntegerValue.ToString();
        });

        SelectedElementId = elementId;
    }
}
```

### 5. Testing Strategies

Implement testable architecture with mocking:

```csharp
// Tests/ViewModelTests.cs
[Test]
public async Task LoadElementsAsync_ShouldPopulateElements()
{
    // Arrange
    var mockBindings = new Mock<ConnectorBindings>();
    mockBindings.Setup(b => b.GetElementsAsync())
               .ReturnsAsync(new List<string> { "Element1", "Element2" });

    var viewModel = new MainViewModel(mockBindings.Object);

    // Act
    await viewModel.LoadElementsCommand.Execute();

    // Assert
    Assert.AreEqual(2, viewModel.Elements.Count);
    Assert.IsFalse(viewModel.IsLoading);
}
```

This comprehensive guide provides everything needed to successfully integrate Avalonia UI with Revit API, from basic setup to advanced patterns and optimizations. The architecture demonstrated here has been proven in production through the Speckle Revit connector and provides a solid foundation for building modern, responsive UIs within Revit add-ins.
```
