using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using Avalonia;
using Avalonia.Platform;
using Avalonia.Win32;
using Avalonia.Controls;
using Avalonia.Threading;

namespace RevitAvalonia.UI;

/// <summary>
/// Specialized Avalonia initializer for Revit host environment
/// <PERSON>les graphics context conflicts and native library loading issues
/// </summary>
public static class AvaloniaInitializer
{
    private static bool _initialized = false;
    private static readonly object _lock = new object();
    private static string _lastError = string.Empty;

    public static bool IsInitialized => _initialized;
    public static string LastError => _lastError;

    /// <summary>
    /// Initialize Avalonia with multiple fallback strategies optimized for Revit
    /// </summary>
    public static bool Initialize()
    {
        lock (_lock)
        {
            if (_initialized) return true;

            try
            {
                // Log system information for diagnostics
                LogSystemInfo();

                // Preload native dependencies
                PreloadNativeDependencies();

                // Try initialization strategies in order of preference
                // Start with the most compatible approach for Revit
                if (TryInitializeWithRevitCompatibleMode() ||
                    TryInitializeWithSoftwareRendering() ||
                    TryInitializeWithWin32Skia() ||
                    TryInitializeWithPlatformDetect() ||
                    TryInitializeMinimal())
                {
                    _initialized = true;
                    Debug.WriteLine("Avalonia initialized successfully for Revit");
                    return true;
                }

                throw new InvalidOperationException("All Avalonia initialization strategies failed");
            }
            catch (Exception ex)
            {
                _lastError = ex.ToString();
                Debug.WriteLine($"Avalonia initialization failed: {ex}");
                return false;
            }
        }
    }

    private static void LogSystemInfo()
    {
        try
        {
            Debug.WriteLine($"OS: {Environment.OSVersion}");
            Debug.WriteLine($"CLR: {Environment.Version}");
            Debug.WriteLine($"Process: {Environment.Is64BitProcess} bit");
            Debug.WriteLine($"Assembly location: {Assembly.GetExecutingAssembly().Location}");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Failed to log system info: {ex.Message}");
        }
    }

    private static void PreloadNativeDependencies()
    {
        try
        {
            var assemblyDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            if (string.IsNullOrEmpty(assemblyDir)) return;

            // Define all possible native library paths and names
            var libraryConfigs = new[]
            {
                // SkiaSharp native libraries
                new { Name = "libSkiaSharp.dll", Paths = new[] {
                    Path.Combine(assemblyDir, "runtimes", "win-x64", "native", "libSkiaSharp.dll"),
                    Path.Combine(assemblyDir, "libSkiaSharp.dll"),
                    Path.Combine(assemblyDir, "runtimes", "win", "native", "libSkiaSharp.dll")
                }},
                // HarfBuzzSharp native libraries - try multiple naming conventions
                new { Name = "libHarfBuzzSharp.dll", Paths = new[] {
                    Path.Combine(assemblyDir, "runtimes", "win-x64", "native", "libHarfBuzzSharp.dll"),
                    Path.Combine(assemblyDir, "libHarfBuzzSharp.dll"),
                    Path.Combine(assemblyDir, "runtimes", "win", "native", "libHarfBuzzSharp.dll"),
                    Path.Combine(assemblyDir, "runtimes", "win-x64", "native", "harfbuzz.dll"),
                    Path.Combine(assemblyDir, "harfbuzz.dll")
                }}
            };

            foreach (var config in libraryConfigs)
            {
                bool loaded = false;
                foreach (var path in config.Paths)
                {
                    if (File.Exists(path))
                    {
                        try
                        {
                            var handle = NativeMethods.LoadLibrary(path);
                            if (handle != IntPtr.Zero)
                            {
                                Debug.WriteLine($"Successfully preloaded {config.Name}: {path}");
                                loaded = true;
                                break;
                            }
                            else
                            {
                                var error = Marshal.GetLastWin32Error();
                                Debug.WriteLine($"LoadLibrary failed for {path}, error: {error}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"Exception loading {path}: {ex.Message}");
                        }
                    }
                    else
                    {
                        Debug.WriteLine($"Library not found: {path}");
                    }
                }
                
                if (!loaded)
                {
                    Debug.WriteLine($"WARNING: Failed to preload {config.Name} from any location");
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Preload dependencies failed: {ex.Message}");
        }
    }

    private static bool TryInitializeWithRevitCompatibleMode()
    {
        try
        {
            Debug.WriteLine("Trying Revit-compatible initialization...");

            // Ensure graphics context isolation from Revit
            IsolateGraphicsContext();

            AppBuilder.Configure<App>()
                .UseWin32()
                .UseSkia()
                .With(new Win32PlatformOptions())
                .With(new SkiaOptions
                {
                    // Force software rendering to avoid GPU conflicts
                    MaxGpuResourceSizeBytes = 0
                })
                .SetupWithoutStarting();

            Debug.WriteLine("Revit-compatible initialization successful");
            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Revit-compatible initialization failed: {ex.Message}");
            Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            return false;
        }
    }

    private static void IsolateGraphicsContext()
    {
        try
        {
            Debug.WriteLine("Isolating graphics context from Revit...");

            // Force GDI+ to initialize in software mode
            Environment.SetEnvironmentVariable("AVALONIA_SOFTWARE_RENDERING", "1");
            Environment.SetEnvironmentVariable("SKIA_SOFTWARE_RENDERING", "1");

            Debug.WriteLine("Graphics context isolation completed");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Graphics context isolation failed: {ex.Message}");
        }
    }

    private static bool TryInitializeWithSoftwareRendering()
    {
        try
        {
            Debug.WriteLine("Trying software rendering initialization...");

            // Use the standard Avalonia App class with Fluent theme
            AppBuilder.Configure<App>()
                .UseWin32()
                .UseSkia()
                .With(new Win32PlatformOptions())
                .SetupWithoutStarting();

            Debug.WriteLine("Software rendering initialization successful");
            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Software rendering failed: {ex.Message}");
            Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            return false;
        }
    }

    private static bool TryInitializeWithWin32Skia()
    {
        try
        {
            Debug.WriteLine("Trying Win32 + Skia initialization...");

            AppBuilder.Configure<App>()
                .UseWin32()
                .UseSkia()
                .With(new Win32PlatformOptions())
                .SetupWithoutStarting();

            Debug.WriteLine("Win32 + Skia initialization successful");
            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Win32 + Skia failed: {ex.Message}");
            Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            return false;
        }
    }

    private static bool TryInitializeWithPlatformDetect()
    {
        try
        {
            Debug.WriteLine("Trying platform detect initialization...");

            AppBuilder.Configure<App>()
                .UsePlatformDetect()
                .SetupWithoutStarting();

            Debug.WriteLine("Platform detect initialization successful");
            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Platform detect failed: {ex.Message}");
            Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            return false;
        }
    }

    private static bool TryInitializeMinimal()
    {
        try
        {
            Debug.WriteLine("Trying minimal initialization...");

            AppBuilder.Configure<App>()
                .UseWin32()
                .With(new Win32PlatformOptions())
                .SetupWithoutStarting();

            Debug.WriteLine("Minimal initialization successful");
            return true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Minimal initialization failed: {ex.Message}");
            Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            return false;
        }
    }

    /// <summary>
    /// Cleanup Avalonia resources when shutting down
    /// </summary>
    public static void Cleanup()
    {
        lock (_lock)
        {
            try
            {
                if (_initialized)
                {
                    Debug.WriteLine("Cleaning up Avalonia resources...");

                    // Cleanup is handled by Avalonia framework
                    _initialized = false;
                    _lastError = string.Empty;

                    Debug.WriteLine("Avalonia cleanup completed");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Avalonia cleanup failed: {ex}");
            }
        }
    }
}
