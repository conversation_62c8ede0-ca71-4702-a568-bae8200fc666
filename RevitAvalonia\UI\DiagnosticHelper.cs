using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Text;

namespace RevitAvalonia.UI;

/// <summary>
/// Provides diagnostic information for troubleshooting Avalonia initialization issues in Revit
/// </summary>
public static class DiagnosticHelper
{
    /// <summary>
    /// Generate a comprehensive diagnostic report for troubleshooting
    /// </summary>
    public static string GenerateDiagnosticReport()
    {
        var report = new StringBuilder();
        
        try
        {
            report.AppendLine("=== Avalonia Revit Integration Diagnostic Report ===");
            report.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            // System Information
            report.AppendLine("--- System Information ---");
            report.AppendLine($"OS: {Environment.OSVersion}");
            report.AppendLine($"CLR Version: {Environment.Version}");
            report.AppendLine($"Process Architecture: {(Environment.Is64BitProcess ? "x64" : "x86")}");
            report.AppendLine($"OS Architecture: {(Environment.Is64BitOperatingSystem ? "x64" : "x86")}");
            report.AppendLine($"Working Directory: {Environment.CurrentDirectory}");
            report.AppendLine();

            // Assembly Information
            report.AppendLine("--- Assembly Information ---");
            var assembly = Assembly.GetExecutingAssembly();
            report.AppendLine($"Assembly Location: {assembly.Location}");
            report.AppendLine($"Assembly Version: {assembly.GetName().Version}");
            report.AppendLine($"Assembly Directory: {Path.GetDirectoryName(assembly.Location)}");
            report.AppendLine();

            // Avalonia Dependencies
            report.AppendLine("--- Avalonia Dependencies ---");
            CheckAvaloniaAssemblies(report);
            report.AppendLine();

            // Native Dependencies
            report.AppendLine("--- Native Dependencies ---");
            CheckNativeDependencies(report);
            report.AppendLine();

            // Graphics Information
            report.AppendLine("--- Graphics Information ---");
            CheckGraphicsCapabilities(report);
            report.AppendLine();

            // Revit Context
            report.AppendLine("--- Revit Context ---");
            CheckRevitContext(report);

        }
        catch (Exception ex)
        {
            report.AppendLine($"Error generating diagnostic report: {ex}");
        }

        return report.ToString();
    }

    private static void CheckAvaloniaAssemblies(StringBuilder report)
    {
        var avaloniaAssemblies = new[]
        {
            "Avalonia.dll",
            "Avalonia.Base.dll",
            "Avalonia.Controls.dll",
            "Avalonia.Win32.dll",
            "Avalonia.Skia.dll",
            "SkiaSharp.dll"
        };

        var assemblyDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        if (string.IsNullOrEmpty(assemblyDir)) return;

        foreach (var assemblyName in avaloniaAssemblies)
        {
            var path = Path.Combine(assemblyDir, assemblyName);
            if (File.Exists(path))
            {
                try
                {
                    var version = FileVersionInfo.GetVersionInfo(path);
                    report.AppendLine($"✓ {assemblyName}: {version.FileVersion}");
                }
                catch (Exception ex)
                {
                    report.AppendLine($"⚠ {assemblyName}: Found but version check failed - {ex.Message}");
                }
            }
            else
            {
                report.AppendLine($"✗ {assemblyName}: Not found");
            }
        }
    }

    private static void CheckNativeDependencies(StringBuilder report)
    {
        var assemblyDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        if (string.IsNullOrEmpty(assemblyDir)) return;

        // Check for SkiaSharp native libraries
        var nativePaths = new[]
        {
            Path.Combine(assemblyDir, "runtimes", "win-x64", "native", "libSkiaSharp.dll"),
            Path.Combine(assemblyDir, "runtimes", "win", "native", "libSkiaSharp.dll"),
            Path.Combine(assemblyDir, "libSkiaSharp.dll")
        };

        bool foundNative = false;
        foreach (var path in nativePaths)
        {
            if (File.Exists(path))
            {
                report.AppendLine($"✓ SkiaSharp native: {path}");
                foundNative = true;
                break;
            }
        }

        if (!foundNative)
        {
            report.AppendLine("✗ SkiaSharp native library not found in expected locations");
        }

        // Check runtimes directory structure
        var runtimesDir = Path.Combine(assemblyDir, "runtimes");
        if (Directory.Exists(runtimesDir))
        {
            report.AppendLine($"✓ Runtimes directory exists: {runtimesDir}");
            var subdirs = Directory.GetDirectories(runtimesDir);
            foreach (var subdir in subdirs)
            {
                report.AppendLine($"  - {Path.GetFileName(subdir)}");
            }
        }
        else
        {
            report.AppendLine("✗ Runtimes directory not found");
        }
    }

    private static void CheckGraphicsCapabilities(StringBuilder report)
    {
        try
        {
            var hdc = NativeMethods.GetDC(IntPtr.Zero);
            if (hdc != IntPtr.Zero)
            {
                var bitsPerPixel = NativeMethods.GetDeviceCaps(hdc, NativeMethods.BITSPIXEL);
                var planes = NativeMethods.GetDeviceCaps(hdc, NativeMethods.PLANES);
                var rasterCaps = NativeMethods.GetDeviceCaps(hdc, NativeMethods.RASTERCAPS);
                
                report.AppendLine($"Bits per pixel: {bitsPerPixel}");
                report.AppendLine($"Color planes: {planes}");
                report.AppendLine($"Palette support: {(rasterCaps & NativeMethods.RC_PALETTE) != 0}");
                
                NativeMethods.ReleaseDC(IntPtr.Zero, hdc);
            }
            else
            {
                report.AppendLine("Could not get device context");
            }
        }
        catch (Exception ex)
        {
            report.AppendLine($"Graphics check failed: {ex.Message}");
        }
    }

    private static void CheckRevitContext(StringBuilder report)
    {
        try
        {
            var revitContext = RevitContext.UiApplication;
            if (revitContext != null)
            {
                report.AppendLine($"✓ Revit context available");
                report.AppendLine($"Revit version: {revitContext.Application.VersionName}");
                report.AppendLine($"Revit build: {revitContext.Application.VersionBuild}");
                
                var doc = revitContext.ActiveUIDocument?.Document;
                if (doc != null)
                {
                    report.AppendLine($"Active document: {doc.Title}");
                    report.AppendLine($"Document path: {doc.PathName}");
                }
                else
                {
                    report.AppendLine("No active document");
                }
            }
            else
            {
                report.AppendLine("✗ Revit context not available");
            }
        }
        catch (Exception ex)
        {
            report.AppendLine($"Revit context check failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Save diagnostic report to a file for later analysis
    /// </summary>
    public static void SaveDiagnosticReport(string? filePath = null)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
            {
                var tempDir = Path.GetTempPath();
                filePath = Path.Combine(tempDir, $"AvaloniaRevitDiagnostic_{DateTime.Now:yyyyMMdd_HHmmss}.txt");
            }

            var report = GenerateDiagnosticReport();
            File.WriteAllText(filePath, report);
            
            Debug.WriteLine($"Diagnostic report saved to: {filePath}");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Failed to save diagnostic report: {ex.Message}");
        }
    }
}
