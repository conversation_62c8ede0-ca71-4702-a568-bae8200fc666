﻿using System;
using System.IO;
using System.Reflection;
using Nice3point.Revit.Toolkit.External;
using RevitAvalonia.Commands;
using RevitAvalonia.UI;

    namespace RevitAvalonia;

    /// <summary>
    ///     Application entry point
    /// </summary>
    [UsedImplicitly]
    public class Application : ExternalApplication
    {
        public override void OnStartup()
        {
            AppDomain.CurrentDomain.AssemblyResolve += OnAssemblyResolve;
            AvaloniaInitializer.Initialize();
            // Host.Start();
            CreateRibbon();
        }

        public override void OnShutdown()
        {
            AppDomain.CurrentDomain.AssemblyResolve -= OnAssemblyResolve;
            // Cleanup Avalonia resources
            AvaloniaInitializer.Cleanup();
            // Host.Stop();
        }
        
        static Assembly OnAssemblyResolve(object sender, ResolveEventArgs args)
        {
            Assembly a = null;
            var name = args.Name.Split(',')[0];
            string path = Path.GetDirectoryName(typeof(Application).Assembly.Location);

            string assemblyFile = Path.Combine(path, name + ".dll");

            if (File.Exists(assemblyFile))
            {
                a = Assembly.LoadFrom(assemblyFile);
            }

            return a;
        }

        private void CreateRibbon()
        {
            var panel = Application.CreatePanel("Commands", "RevitAvalonia");

            panel.AddPushButton<StartupCommand>("Execute")
                .SetImage("/RevitAvalonia;component/Resources/Icons/RibbonIcon16.png")
                .SetLargeImage("/RevitAvalonia;component/Resources/Icons/RibbonIcon32.png");
        }
    }