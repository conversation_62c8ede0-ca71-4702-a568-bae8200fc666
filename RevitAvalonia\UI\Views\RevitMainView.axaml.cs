using System;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;

namespace RevitAvalonia.UI.Views;

public partial class RevitMainView : UserControl
{
    private TextBlock? _revitVersionText;
    private TextBlock? _documentText;
    private TextBlock? _statusText;
    private TextBlock? _logText;

    public RevitMainView()
    {
        InitializeComponent();
        UpdateRevitInfo();
    }

    private void InitializeComponent()
    {
        AvaloniaXamlLoader.Load(this);
        
        _revitVersionText = this.FindControl<TextBlock>("RevitVersionText");
        _documentText = this.FindControl<TextBlock>("DocumentText");
        _statusText = this.FindControl<TextBlock>("StatusText");
        _logText = this.FindControl<TextBlock>("LogText");
    }

    private void UpdateRevitInfo()
    {
        try
        {
            var uiApp = RevitContext.UiApplication;
            if (uiApp != null)
            {
                if (_revitVersionText != null)
                    _revitVersionText.Text = uiApp.Application.VersionName;

                var doc = uiApp.ActiveUIDocument?.Document;
                if (_documentText != null)
                {
                    _documentText.Text = doc?.Title ?? "No active document";
                }
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Error updating Revit info: {ex.Message}");
        }
    }

    private void OnGetDocumentInfoClick(object? sender, RoutedEventArgs e)
    {
        try
        {
            var uiApp = RevitContext.UiApplication;
            var doc = uiApp?.ActiveUIDocument?.Document;
            
            if (doc == null)
            {
                LogMessage("No active document found.");
                return;
            }

            var info = $"Document: {doc.Title}\n" +
                      $"Path: {doc.PathName}\n" +
                      $"Is Modified: {doc.IsModified}\n" +
                      $"Units: {doc.GetUnits().GetFormatOptions(SpecTypeId.Length).GetUnitTypeId()}";
            
            LogMessage($"Document Info:\n{info}");
            
            if (_statusText != null)
                _statusText.Text = "Document info retrieved";
        }
        catch (Exception ex)
        {
            LogMessage($"Error getting document info: {ex.Message}");
        }
    }

    private void OnCountElementsClick(object? sender, RoutedEventArgs e)
    {
        try
        {
            var uiApp = RevitContext.UiApplication;
            var doc = uiApp?.ActiveUIDocument?.Document;
            
            if (doc == null)
            {
                LogMessage("No active document found.");
                return;
            }

            var collector = new FilteredElementCollector(doc);
            var allElements = collector.WhereElementIsNotElementType().ToElements();
            
            LogMessage($"Total elements in document: {allElements.Count}");
            
            if (_statusText != null)
                _statusText.Text = $"Found {allElements.Count} elements";
        }
        catch (Exception ex)
        {
            LogMessage($"Error counting elements: {ex.Message}");
        }
    }

    private void OnShowMessageClick(object? sender, RoutedEventArgs e)
    {
        try
        {
            TaskDialog.Show("Avalonia Add-in", 
                "Hello from Avalonia UI running inside Revit!\n\n" +
                "This demonstrates successful integration between:\n" +
                "• Avalonia UI 11.3.2\n" +
                "• Revit API\n" +
                "• AvaloniaHwndHost embedding");
            
            LogMessage("Message dialog shown successfully.");
            
            if (_statusText != null)
                _statusText.Text = "Message displayed";
        }
        catch (Exception ex)
        {
            LogMessage($"Error showing message: {ex.Message}");
        }
    }

    private void OnClearLogClick(object? sender, RoutedEventArgs e)
    {
        if (_logText != null)
        {
            _logText.Text = "Log cleared.";
        }
        
        if (_statusText != null)
            _statusText.Text = "Log cleared";
    }

    private void LogMessage(string message)
    {
        if (_logText != null)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var currentText = _logText.Text ?? "";
            _logText.Text = $"{currentText}\n[{timestamp}] {message}";
        }
    }
}
